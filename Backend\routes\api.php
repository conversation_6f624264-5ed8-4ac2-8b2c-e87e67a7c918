<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\FirebaseTestController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Firebase Testing Routes
Route::prefix('firebase')->group(function () {
    Route::get('/test', [FirebaseTestController::class, 'testConnection']);
    Route::post('/test/notification', [FirebaseTestController::class, 'sendTestNotification']);
    Route::get('/status', [FirebaseTestController::class, 'getStatus']);
});

// Test route to verify API is working
Route::get('/test', function () {
    return response()->json([
        'message' => 'API is working',
        'timestamp' => now()->toISOString(),
        'firebase_configured' => file_exists(storage_path('app/vault/firebase_service.json'))
    ]);
});
