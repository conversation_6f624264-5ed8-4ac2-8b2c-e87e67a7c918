<variant
    name="release"
    package="slayer.bg.launcher.bg_launcher"
    minSdkVersion="16"
    targetSdkVersion="16"
    mergedManifest="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\intermediates\default_proguard_files\global\proguard-android.txt-8.10.0"
    partialResultsDir="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="slayer.bg.launcher.bg_launcher"
      generatedSourceFolders="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cde2f4557796170399bbc6ab80157263\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
