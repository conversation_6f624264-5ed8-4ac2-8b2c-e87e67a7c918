<?php

namespace App\Http\Livewire\Auth;

use App\Http\Livewire\BaseLivewireComponent;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class LoginLivewire extends BaseLivewireComponent
{
    public $email;
    public $password;
    public $remember = false;



    public function loadAccount(int $type = 0)
    {
        if ($type == 0) {
            $this->email = "<EMAIL>";
        } else if ($type == 1) {
            $this->email = "<EMAIL>";
        } else if ($type == 2) {
            $this->email = "<EMAIL>";
        } else if ($type == 3) {
            $this->email = "<EMAIL>";
        } else if ($type == 4) {
            $this->email = "<EMAIL>";
        } else {
            $this->email = "<EMAIL>";
        }
        $this->password = "password";
    }

    public function login()
    {


        $this->validate(
            [
                "email" => "required|email|exists:users",
                "password" => "required|string",
            ],
            [
                "email.exists" => __("Email not associated with any account")
            ]
        );

        //
        $user = User::where('email', $this->email)->first();

        if ($user->hasAnyRole('client', 'driver')) {
            $this->showErrorAlert("Unauthorized Access");
            return;
        } else if (!$user->is_active) {
            $this->showErrorAlert(__("Account is not active. Please contact us"));
            return;
        }

        if (Hash::check($this->password, $user->password) && Auth::attempt(['email' => $this->email, 'password' => $this->password], $this->remember)) {
            //
            $user->syncFCMToken($this->fcmToken);

            // Check if 2FA is enabled for this user
            if (!empty($user->two_factor_secret) && !empty($user->two_factor_confirmed_at)) {
                // Redirect to 2FA verification page
                return redirect()->route('two-factor.verify');
            }

            return redirect()->intended(route('dashboard'));
        } else {
            $this->showErrorAlert(__("Invalid Credentials. Please check your credentials and try again"));
        }
    }

    public function render()
    {
        return view('livewire.auth.login')->layout('layouts.auth');
    }
}
