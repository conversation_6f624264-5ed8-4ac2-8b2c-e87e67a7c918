<variant
    name="release"
    package="io.flutter.plugins.videoplayer"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\intermediates\default_proguard_files\global\proguard-android.txt-8.10.0"
    partialResultsDir="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.videoplayer"
      generatedSourceFolders="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4af70b34c41ffa06a032ef829f847ac\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
