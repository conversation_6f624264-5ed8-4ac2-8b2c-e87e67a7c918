<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\bg_launcher-0.1.0\android"
    name=":bg_launcher"
    type="LIBRARY"
    maven="slayer.bg.launcher.bg_launcher:bg_launcher:1.0"
    agpVersion="8.10.0"
    buildFolder="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\bg_launcher"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-31\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-31"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
