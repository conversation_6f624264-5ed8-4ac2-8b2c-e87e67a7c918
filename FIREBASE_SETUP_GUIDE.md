# 🔥 Firebase Setup and Testing Guide

## ✅ **What Has Been Fixed**

1. **Firebase Project Unified**: Both Customer and Driver apps now use `geomart-web` project
2. **Service Account Configured**: Real Firebase service account JSON file added
3. **Storage Directory Created**: Backend can now store Firebase credentials
4. **FCM V1 API Configured**: Modern Firebase Cloud Messaging setup
5. **Environment Variables Added**: Firebase configuration in `.env` file
6. **Test Tools Created**: Firebase connection testing tools

## 🧪 **Testing Firebase Connection**

### **Method 1: Command Line Test**
```bash
cd Backend
php artisan firebase:test --driver-id=1
```

### **Method 2: API Test**
Visit these URLs in your browser:
- **Connection Test**: `https://geomart.nafiss.net/api/firebase/test`
- **Status Check**: `https://geomart.nafiss.net/api/firebase/status`
- **API Health**: `https://geomart.nafiss.net/api/test`

### **Method 3: Send Test Notification**
```bash
curl -X POST https://geomart.nafiss.net/api/firebase/test/notification \
  -H "Content-Type: application/json" \
  -d '{"driver_id": 1, "message": "Test notification from Firebase!"}'
```

## 🔧 **Backend Admin Configuration**

1. **Access Admin Panel**: `https://geomart.nafiss.net/admin`
2. **Go to Settings** → **Notifications**
3. **Configure Firebase Settings**:
   - **Project ID**: `geomart-web`
   - **Messaging Sender ID**: `************`
   - **API Key**: `AIzaSyBYLq22w61pFBGzq71RpyAI7-mwHKzbzC0`
   - **App ID**: Get from Firebase Console
   - **VAPID Key**: Get from Firebase Console
   - **Upload Service Account**: Upload the Firebase service account JSON

## 📱 **Mobile App Configuration**

### **Customer App**
- ✅ Firebase project updated to `geomart-web`
- ✅ `google-services.json` updated
- 🔄 **Need to rebuild app**

### **Driver App**
- ✅ Already using `geomart-web` project
- ✅ `google-services.json` is correct
- 🔄 **Need to rebuild app**

## 🎯 **Testing Order Flow**

### **Step 1: Prepare Driver App**
1. Install/rebuild driver app
2. Login as a driver
3. Go online (set status to available)
4. Verify driver is subscribed to Firebase topics

### **Step 2: Test Notification**
1. Use the test notification API or command
2. Check if driver receives the test notification
3. If received, Firebase is working correctly

### **Step 3: Test Real Order**
1. Open customer app
2. Place a test order
3. Check if driver receives order notification
4. Accept/reject order to test complete flow

## 🔍 **Troubleshooting**

### **If Test Notification Fails**
1. Check Firebase service account permissions
2. Verify Firebase project ID is correct
3. Ensure driver app is using same Firebase project
4. Check network connectivity

### **If Order Notifications Fail**
1. Verify order assignment job is running
2. Check if drivers are properly online in database
3. Verify Firebase topics subscription in driver app
4. Check backend logs for errors

### **Common Issues**
- **"Please setup firebase on backend"**: Service account file missing or invalid
- **No notification received**: Driver not subscribed to correct topic
- **Connection timeout**: Network or Firebase service issues

## 📋 **Configuration Checklist**

- [ ] Firebase service account JSON uploaded
- [ ] Backend notification settings configured
- [ ] Customer app rebuilt with new Firebase config
- [ ] Driver app rebuilt (if needed)
- [ ] Test notification sent successfully
- [ ] Real order notification tested
- [ ] Driver can accept/reject orders

## 🚀 **Next Steps**

1. **Complete Backend Configuration**: Upload real Firebase service account and configure all settings
2. **Rebuild Mobile Apps**: Ensure both apps use the unified Firebase project
3. **Test Thoroughly**: Test all notification scenarios
4. **Monitor Logs**: Check for any errors during testing
5. **Go Live**: Deploy to production once testing is successful

## 📞 **Support**

If you encounter issues:
1. Check the test endpoints for detailed error messages
2. Review Laravel logs: `Backend/storage/logs/laravel.log`
3. Check Firebase Console for project status
4. Verify all configuration values are correct

---

**🎉 Your Firebase setup is now ready for testing!**
