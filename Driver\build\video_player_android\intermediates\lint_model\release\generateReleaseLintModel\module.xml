<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\video_player_android-2.8.2\android"
    name=":video_player_android"
    type="LIBRARY"
    maven="io.flutter.plugins.videoplayer:video_player_android:1.0-SNAPSHOT"
    agpVersion="8.10.0"
    buildFolder="C:\Users\<USER>\Desktop\GeoMartProject\FixProject\Driver\build\video_player_android"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="AndroidGradlePluginVersion,InvalidPackage,GradleDependency,NewerVersionAvailable"
      abortOnError="true"
      absolutePaths="true"
      checkAllWarnings="true"
      warningsAsErrors="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="AndroidGradlePluginVersion"
        severity="IGNORE" />
      <severity
        id="GradleDependency"
        severity="IGNORE" />
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
      <severity
        id="NewerVersionAvailable"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
