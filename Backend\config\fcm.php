<?php

return [
    'driver' => env('FCM_PROTOCOL', 'http'),
    'log_enabled' => false,

    // Legacy FCM HTTP v1 (for backward compatibility)
    'http' => [
        'server_key' => env('FCM_SERVER_KEY', ''),
        'sender_id' => env('FCM_SENDER_ID', '************'),
        'server_send_url' => 'https://fcm.googleapis.com/fcm/send',
        'server_group_url' => 'https://android.googleapis.com/gcm/notification',
        'timeout' => 30.0, // in second
    ],

    // Firebase Cloud Messaging V1 API Configuration
    'v1' => [
        'service_account_path' => env('FCM_SERVICE_ACCOUNT_PATH', 'storage/app/vault/firebase_service.json'),
        'project_id' => env('FIREBASE_PROJECT_ID', 'geomart-web'),
        'sender_id' => env('FCM_SENDER_ID', '************'),
        'vapid_public_key' => env('VAPID_PUBLIC_KEY', ''),
        'vapid_private_key' => env('VAPID_PRIVATE_KEY', ''),
        'timeout' => 30.0,
    ],
];
