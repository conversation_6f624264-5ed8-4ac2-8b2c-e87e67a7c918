<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Traits\FirebaseAuthTrait;
use App\Traits\FirebaseMessagingTrait;

class TestFirebaseConnection extends Command
{
    use FirebaseAuthTrait, FirebaseMessagingTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'firebase:test {--driver-id=1 : Driver ID to test notification}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Firebase connection and send a test notification to a driver';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔥 Testing Firebase Connection...');
        
        try {
            // Test 1: Firebase Factory Connection
            $this->info('1. Testing Firebase Factory...');
            $factory = $this->getFirebaseFactory();
            $this->info('✅ Firebase Factory initialized successfully');

            // Test 2: Firebase Auth
            $this->info('2. Testing Firebase Auth...');
            $auth = $this->getFirebaseAuth();
            $this->info('✅ Firebase Auth initialized successfully');

            // Test 3: Firebase Messaging
            $this->info('3. Testing Firebase Messaging...');
            $messaging = $this->getFirebaseMessaging();
            $this->info('✅ Firebase Messaging initialized successfully');

            // Test 4: Firebase Firestore
            $this->info('4. Testing Firebase Firestore...');
            $firestore = $this->getFirebaseStore();
            $this->info('✅ Firebase Firestore initialized successfully');

            // Test 5: Send test notification to driver
            $driverId = $this->option('driver-id');
            $this->info("5. Sending test notification to driver ID: {$driverId}...");
            
            $driverTopic = "d_{$driverId}";
            $title = "🧪 Test Notification";
            $body = "This is a test notification from Firebase. If you receive this, Firebase is working correctly!";
            $data = [
                'type' => 'test',
                'timestamp' => now()->toISOString(),
                'driver_id' => $driverId,
            ];

            $this->sendFirebaseNotification(
                $driverTopic,
                $title,
                $body,
                $data,
                $onlyData = false,
                "test_channel",
                $noSound = false
            );

            $this->info('✅ Test notification sent successfully');
            $this->info("📱 Check driver app (ID: {$driverId}) for the test notification");

            // Test 6: Create custom token
            $this->info('6. Testing custom token creation...');
            $customToken = $auth->createCustomToken("test_user_123");
            $this->info('✅ Custom token created successfully');

            $this->info('');
            $this->info('🎉 All Firebase tests passed successfully!');
            $this->info('');
            $this->info('📋 Configuration Summary:');
            $this->info("   Project ID: geomart-web");
            $this->info("   Service Account: <EMAIL>");
            $this->info("   Driver Topic: {$driverTopic}");
            $this->info('');
            $this->info('🔍 Next Steps:');
            $this->info('   1. Check if driver app received the test notification');
            $this->info('   2. If notification not received, check driver app Firebase topic subscription');
            $this->info('   3. Verify driver app is using the same Firebase project (geomart-web)');
            $this->info('   4. Test placing an actual order to verify the complete flow');

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Firebase connection test failed!');
            $this->error('Error: ' . $e->getMessage());
            $this->error('');
            $this->error('🔧 Troubleshooting:');
            $this->error('   1. Check if Firebase service account file exists and is valid');
            $this->error('   2. Verify Firebase project ID is correct');
            $this->error('   3. Ensure service account has proper permissions');
            $this->error('   4. Check network connectivity to Firebase services');
            
            return Command::FAILURE;
        }
    }
}
