<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Traits\FirebaseAuthTrait;
use App\Traits\FirebaseMessagingTrait;

class FirebaseTestController extends Controller
{
    use FirebaseAuthTrait, FirebaseMessagingTrait;

    /**
     * Test Firebase connection and configuration
     */
    public function testConnection()
    {
        try {
            $results = [];

            // Test Firebase Factory
            $factory = $this->getFirebaseFactory();
            $results['factory'] = '✅ Connected';

            // Test Firebase Auth
            $auth = $this->getFirebaseAuth();
            $results['auth'] = '✅ Connected';

            // Test Firebase Messaging
            $messaging = $this->getFirebaseMessaging();
            $results['messaging'] = '✅ Connected';

            // Test Firebase Firestore
            $firestore = $this->getFirebaseStore();
            $results['firestore'] = '✅ Connected';

            // Get project info
            $projectId = setting('projectId', 'geomart-web');
            $messagingSenderId = setting('messagingSenderId', '************');

            return response()->json([
                'status' => 'success',
                'message' => 'Firebase connection successful',
                'tests' => $results,
                'configuration' => [
                    'project_id' => $projectId,
                    'messaging_sender_id' => $messagingSenderId,
                    'service_account_email' => '<EMAIL>',
                    'service_account_path' => 'storage/app/vault/firebase_service.json'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Firebase connection failed',
                'error' => $e->getMessage(),
                'troubleshooting' => [
                    'Check if Firebase service account file exists',
                    'Verify Firebase project ID is correct',
                    'Ensure service account has proper permissions',
                    'Check network connectivity to Firebase services'
                ]
            ], 500);
        }
    }

    /**
     * Send test notification to a driver
     */
    public function sendTestNotification(Request $request)
    {
        $request->validate([
            'driver_id' => 'required|integer',
            'message' => 'string|max:255'
        ]);

        try {
            $driverId = $request->driver_id;
            $customMessage = $request->message ?? 'This is a test notification from Firebase!';
            
            $driverTopic = "d_{$driverId}";
            $title = "🧪 Test Notification";
            $body = $customMessage;
            $data = [
                'type' => 'test',
                'timestamp' => now()->toISOString(),
                'driver_id' => (string)$driverId,
                'test_id' => uniqid('test_'),
            ];

            $this->sendFirebaseNotification(
                $driverTopic,
                $title,
                $body,
                $data,
                $onlyData = false,
                "test_channel",
                $noSound = false
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Test notification sent successfully',
                'details' => [
                    'driver_id' => $driverId,
                    'topic' => $driverTopic,
                    'title' => $title,
                    'body' => $body,
                    'sent_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to send test notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Firebase configuration status
     */
    public function getStatus()
    {
        $serviceAccountPath = storage_path('app/vault/firebase_service.json');
        $serviceAccountExists = file_exists($serviceAccountPath);
        
        $config = [
            'service_account_exists' => $serviceAccountExists,
            'service_account_path' => $serviceAccountPath,
            'project_id' => setting('projectId', 'geomart-web'),
            'messaging_sender_id' => setting('messagingSenderId', '************'),
            'api_key' => setting('apiKey', 'Not configured'),
            'app_id' => setting('appId', 'Not configured'),
            'vapid_key' => setting('vapidKey', 'Not configured'),
        ];

        $status = $serviceAccountExists ? 'ready' : 'needs_configuration';

        return response()->json([
            'status' => $status,
            'configuration' => $config,
            'recommendations' => $this->getRecommendations($config)
        ]);
    }

    private function getRecommendations($config)
    {
        $recommendations = [];

        if (!$config['service_account_exists']) {
            $recommendations[] = 'Upload Firebase service account JSON file';
        }

        if ($config['api_key'] === 'Not configured' || $config['api_key'] === 'XXXXXXXXXXXX') {
            $recommendations[] = 'Configure Firebase API Key in backend settings';
        }

        if ($config['app_id'] === 'Not configured') {
            $recommendations[] = 'Configure Firebase App ID in backend settings';
        }

        if ($config['vapid_key'] === 'Not configured' || $config['vapid_key'] === 'XXXXXXXXXXXX') {
            $recommendations[] = 'Configure VAPID Key for web push notifications';
        }

        if (empty($recommendations)) {
            $recommendations[] = 'Configuration looks good! Test notifications to verify functionality.';
        }

        return $recommendations;
    }
}
